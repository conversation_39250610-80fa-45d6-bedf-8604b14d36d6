# Rustfmt configuration for Shreder project

# Keep assignment statements on single line
max_width = 120
use_small_heuristics = "Max"

# Control line breaking behavior
chain_width = 120
single_line_if_else_max_width = 120
where_single_line = true

# Control spacing and grouping
blank_lines_upper_bound = 2
blank_lines_lower_bound = 0

# Keep method chains on single line when possible
chain_width = 120
chains_overflow_last = false

# Control assignment formatting
use_field_init_shorthand = true
force_explicit_abi = false

# Control imports and use statements
imports_granularity = "Crate"
group_imports = "StdExternalCrate"

# Control function formatting
fn_single_line = true
where_single_line = true

# Control struct and enum formatting
struct_field_align_threshold = 20
enum_discrim_align_threshold = 20

# Control comment formatting
normalize_comments = true
wrap_comments = true
comment_width = 100

# Control string formatting
format_strings = true

# Control macro formatting
format_macro_matchers = true
format_macro_bodies = true
