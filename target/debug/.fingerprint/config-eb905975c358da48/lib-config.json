{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 1398949370558892206, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 14645328664580213977], [1238778183371849706, "yaml_rust2", false, 3852773096708079266], [2244620803250265856, "ron", false, 1229350325974633469], [2356429411733741858, "ini", false, 7627912881497434494], [6517602928339163454, "path<PERSON><PERSON>", false, 16167932994736035655], [9689903380558560274, "serde", false, 17543531021294601102], [11946729385090170470, "async_trait", false, 7172890008290943547], [13475460906694513802, "convert_case", false, 10963287255713169168], [14718834678227948963, "winnow", false, 10371683915642526214], [15367738274754116744, "serde_json", false, 15348160935390539816], [15609422047640926750, "toml", false, 668247944883746756]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-eb905975c358da48/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}