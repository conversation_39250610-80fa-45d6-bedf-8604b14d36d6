{"rustc": 15497389221046826682, "features": "[\"std\"]", "declared_features": "[\"default\", \"logging\", \"pattern\", \"perf\", \"perf-backtrack\", \"perf-cache\", \"perf-dfa\", \"perf-dfa-full\", \"perf-inline\", \"perf-literal\", \"perf-onepass\", \"std\", \"unicode\", \"unicode-age\", \"unicode-bool\", \"unicode-case\", \"unicode-gencat\", \"unicode-perl\", \"unicode-script\", \"unicode-segment\", \"unstable\", \"use_std\"]", "target": 5796931310894148030, "profile": 8276155916380437441, "path": 15033791335116528145, "deps": [[555019317135488525, "regex_automata", false, 12335437015152900121], [9408802513701742484, "regex_syntax", false, 14977198434408160083]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/regex-7e413eb5a4dfe295/dep-lib-regex", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}