{"rustc": 15497389221046826682, "features": "[\"default-hasher\", \"inline-more\"]", "declared_features": "[\"alloc\", \"allocator-api2\", \"core\", \"default\", \"default-hasher\", \"equivalent\", \"inline-more\", \"nightly\", \"raw-entry\", \"rayon\", \"rustc-dep-of-std\", \"rustc-internal-api\", \"serde\"]", "target": 13796197676120832388, "profile": 8276155916380437441, "path": 6057324441630989246, "deps": [[10842263908529601448, "<PERSON><PERSON><PERSON>", false, 16051547221411199753]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/hashbrown-4c85a8c581bf297c/dep-lib-hashbrown", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}