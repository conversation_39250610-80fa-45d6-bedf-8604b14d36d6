{"rustc": 15497389221046826682, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 5134834939135515267, "deps": [[1988483478007900009, "unicode_ident", false, 15483803952892883817], [3060637413840920116, "proc_macro2", false, 12622160946388413999], [17990358020177143287, "quote", false, 14745520146559958993]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-08267739849918d1/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}