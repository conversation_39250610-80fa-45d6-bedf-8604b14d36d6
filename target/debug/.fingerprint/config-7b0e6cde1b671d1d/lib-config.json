{"rustc": 15497389221046826682, "features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "declared_features": "[\"async\", \"async-trait\", \"convert-case\", \"convert_case\", \"default\", \"indexmap\", \"ini\", \"json\", \"json5\", \"json5_rs\", \"preserve_order\", \"ron\", \"rust-ini\", \"serde_json\", \"toml\", \"yaml\", \"yaml-rust2\"]", "target": 4953464226640322992, "profile": 18330098564635666122, "path": 10162044332273838614, "deps": [[1213098572879462490, "json5_rs", false, 15769324222242648937], [1238778183371849706, "yaml_rust2", false, 4226907422720225728], [2244620803250265856, "ron", false, 3781724790868506684], [2356429411733741858, "ini", false, 13357730373645291409], [6517602928339163454, "path<PERSON><PERSON>", false, 9763560483937157356], [9689903380558560274, "serde", false, 10136608106485989546], [11946729385090170470, "async_trait", false, 7172890008290943547], [13475460906694513802, "convert_case", false, 18218203473227809642], [14718834678227948963, "winnow", false, 1146924817102495470], [15367738274754116744, "serde_json", false, 10280978127336345074], [15609422047640926750, "toml", false, 11024745599889812095]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/config-7b0e6cde1b671d1d/dep-lib-config", "checksum": false}}], "rustflags": [], "config": 15564086925336547597, "compile_kind": 0}