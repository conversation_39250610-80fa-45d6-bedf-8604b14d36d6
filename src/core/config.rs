use anyhow::{Context, Result};
use config::{Config, Environment, File};
use std::path::Path;
use validator::Validate;

use crate::config::app::AppConfig;

pub fn load_config() -> Result<AppConfig> {
    load_config_from_path("config")
}

pub fn load_config_from_path<P: AsRef<Path>>(config_path: P) -> Result<AppConfig> {
    let config_path = config_path.as_ref();
    let mut builder = Config::builder().add_source(Config::try_from(&AppConfig::default())?);

    if config_path.with_extension("toml").exists() {
        builder =
            builder.add_source(File::with_name(&config_path.with_extension("toml").to_string_lossy()).required(false));
    }

    if config_path.with_extension("json").exists() {
        builder =
            builder.add_source(File::with_name(&config_path.with_extension("json").to_string_lossy()).required(false));
    }

    builder = builder.add_source(Environment::default().separator("_").ignore_empty(true));

    let config = builder.build().context("Failed to build configuration")?;
    let app_config: AppConfig = config.try_deserialize().context("Failed to deserialize configuration")?;

    app_config.validate().context("Configuration validation failed")?;

    Ok(app_config)
}
