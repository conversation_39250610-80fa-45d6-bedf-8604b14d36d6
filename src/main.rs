mod config;
mod core;

use anyhow::Result;
use core::config::AppConfig;

fn main() -> Result<()> {
    println!("=== Shreder Configuration System Test ===");

    println!("\n1. Testing default configuration:");
    let default_config = AppConfig::default();
    println!("Default config: {:#?}", default_config);

    println!("\n2. Testing configuration loading:");
    match AppConfig::load() {
        Ok(config) => {
            println!("Loaded config: {:#?}", config);
            println!("✅ Configuration loaded and validated successfully!");
        }
        Err(e) => {
            println!("⚠️  No config file found, using defaults: {}", e);
            println!("Default config will be used: {:#?}", default_config);
        }
    }

    println!("\n3. Testing environment variable override:");
    println!("Try setting environment variables like:");
    println!("  LOGGER_LEVEL=debug");
    println!("  LOGGER_FORMAT=pretty");
    println!("  LOGGER_OUTPUT=stderr");
    println!("Then run the program again to see the override effect.");

    Ok(())
}
