use serde::{Deserialize, Serialize};
use validator::Validate;

#[derive(Debug, Deserialize, Serialize, Validate)]
pub struct LoggerConfig {
    #[validate(custom(function = "validate_log_level"))]
    pub level: String,

    #[serde(default = "default_format")]
    pub format: String,

    #[serde(default = "default_output")]
    pub output: String,
}

impl Default for LoggerConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: default_format(),
            output: default_output(),
        }
    }
}

fn default_format() -> String {
    "json".to_string()
}

fn default_output() -> String {
    "stdout".to_string()
}

fn validate_log_level(level: &str) -> Result<(), validator::ValidationError> {
    match level {
        "trace" | "debug" | "info" | "warn" | "error" => Ok(()),
        _ => Err(validator::ValidationError::new("invalid_log_level")),
    }
}
