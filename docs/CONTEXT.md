# CONTEXT.md - Shreder Project Analysis

## Tổng quan dự án

**Shreder** là một ứng dụng CLI được viết bằng Rust, đóng vai trò như một proxy trung gian giữa dịch vụ Jito Shredstream và các client applications. Mục tiêu chính là xử lý real-time data từ Solana blockchain với độ trễ cực thấp (nanoseconds level).

### Mục đích chính

-   Kết nối đến multiple Jito Shredstream endpoints đồng thời
-   Deserialize và extract transactions từ raw Entry data
-   Deduplication và filtering intelligent
-   Broadcasting processed data qua WebSocket cho clients
-   Đạt hiệu năng cao với zero downtime trong production

### Giá trị cốt lõi

-   **Ultra-low latency**: Xử lý data trong nanoseconds
-   **High reliability**: 24/7 uptime với automatic failover
-   **Scalability**: Xử lý hàng trăm nghìn entries đồng thời
-   **Data accuracy**: 100% chính xác trong data processing

## Kiến trúc và công nghệ

### Tech Stack

-   **Language**: Rust (stable latest version)
-   **Protocol**: GRPC (input) + WebSocket (output)
-   **Data Format**: Protocol Buffers + Bincode deserialization
-   **Build System**: Cargo với cross-compilation support

### Core Components

1. **GRPC Client**: Kết nối multiple Jito endpoints
2. **Entry Processor**: Deserialize và extract transactions
3. **Deduplication Engine**: Loại bỏ duplicate entries
4. **Filter System**: Account-based và transaction filtering
5. **WebSocket Server**: Real-time broadcasting
6. **Monitoring System**: Performance tracking và health checks

### Data Flow Architecture

```
Multiple Jito Endpoints → Entry Reception → Deduplication →
Transaction Extraction → Filtering → Enrichment → WebSocket Broadcasting
```

## Cấu trúc thư mục chi tiết

### Source Code Organization

```
src/
├── main.rs              # Entry point (simple Hello World)
├── lib.rs               # Library root (exposes modules for testing)
├── common/              # Shared modules (tất cả file trống)
│   ├── broadcaster.rs   # WebSocket broadcasting logic
│   ├── client.rs        # GRPC client implementation
│   ├── monitor.rs       # System monitoring utilities
│   ├── processor.rs     # Entry processing logic
│   └── server.rs        # Server management
├── config/              # Configuration modules
│   ├── mod.rs           # Module declarations (✅ hoàn thành)
│   ├── app.rs           # AppConfig struct (✅ hoàn thành)
│   ├── logger.rs        # Logger config schema (✅ hoàn thành)
│   ├── endpoints.rs     # GRPC endpoints configuration
│   ├── filters.rs       # Transaction filtering rules
│   ├── monitor.rs       # Monitoring configuration
│   └── server.rs        # Server configuration
├── core/                # Core functionality
│   ├── mod.rs           # Module declarations (✅ hoàn thành)
│   ├── config.rs        # Config loading logic (✅ hoàn thành)
│   └── logger.rs        # Core logging implementation
└── utils/               # Reusable utilities (file trống)
    └── mod.rs           # Module declarations
```

### Infrastructure Files

-   **protos/shredstream.proto**: GRPC service definitions (đã hoàn thiện)
-   **Cargo.toml**: Package config (chỉ có basic info, chưa có dependencies)
-   **Makefile**: Build automation (đã hoàn thiện với cross-compilation)
-   **clippy.toml**: Linting configuration (đã hoàn thiện)
-   **releases/**: Pre-built binaries (có 3 binaries đã build)

## Trạng thái hiện tại

### Đã hoàn thành

1. **Project Structure**: Cấu trúc thư mục và file đã được setup đầy đủ
2. **Protocol Definition**: File shredstream.proto đã hoàn thiện với đầy đủ message types
3. **Build System**: Makefile với support cho multiple platforms
4. **Code Quality Tools**: Clippy configuration với strict rules
5. **Binary Releases**: Đã có pre-built binaries cho 3 platforms
6. **Core Configuration System**: Hoàn thành Task 1.1 với đầy đủ functionality
7. **Dependencies Management**: Setup dependencies với cargo add approach
8. **Module Architecture**: Refactored configuration structure theo best practices

### Chưa hoàn thành (Critical)

1. **Core Logging System**: Task 1.2 chưa bắt đầu
2. **GRPC Client Implementation**: Phase 2 chưa bắt đầu
3. **Entry Processing Engine**: Phase 3 chưa bắt đầu
4. **WebSocket Server**: Phase 4 chưa bắt đầu
5. **Main Application Integration**: Phase 5 chưa bắt đầu

## Dependencies và cấu hình quan trọng

### Dependency Management Strategy

-   **Incremental Installation**: Dependencies được cài đặt khi cần thiết cho module đang implement
-   **User Selection**: User quyết định chọn thư viện cụ thể nào sẽ được sử dụng
-   **AI Consultation**: AI phải hỏi user về lựa chọn thư viện trước khi cài đặt

### Current Dependencies (đã cài đặt)

-   **Configuration**: config v0.15.11 (TOML/JSON support)
-   **Validation**: validator v0.20.0 (Zod-like validation)
-   **Serialization**: serde v1.0.219 (với derive features)
-   **Error Handling**: anyhow v1.0.98

### Potential Dependencies (sẽ được thêm theo nhu cầu)

-   **GRPC**: tonic, prost cho Protocol Buffers
-   **WebSocket**: tokio-tungstenite hoặc axum với WebSocket support
-   **Async Runtime**: tokio với full features
-   **Serialization**: bincode cho Entry deserialization
-   **Logging**: tracing, tracing-subscriber
-   **Monitoring**: metrics, prometheus client

### Configuration Requirements

-   Multiple GRPC endpoint URLs
-   WebSocket server binding configuration
-   Account filtering rules
-   Logging levels và output formats
-   Monitoring metrics endpoints
-   Performance tuning parameters

## Quy ước code và tiêu chuẩn

### Code Standards

-   Rust stable latest version
-   Strict adherence to clippy rules (defined in clippy.toml)
-   Modular architecture với clear separation of concerns
-   Reusable utilities trong utils/ directory
-   English-only trong source code
-   **ZERO COMMENTS POLICY**: Tuyệt đối không viết comments trong source code
-   Code must be self-documenting và clear enough

### Performance Requirements

-   Nanosecond-level processing latency
-   Handle hundreds of thousands of entries simultaneously
-   Maximum CPU và memory utilization
-   Zero downtime operation
-   Production-ready optimization

### Development Guidelines

-   Respect existing directory structure
-   **Pre-Implementation Consultation**: Mandatory consultation với user trước mỗi implementation
-   **User Approval Gates**: Required ở consultation và testing completion phases
-   **Incremental Dependency Management**: Chỉ cài dependency khi thực sự cần thiết
-   **User-Driven Library Selection**: AI phải hỏi user về lựa chọn thư viện trước khi cài đặt
-   **Module Independence**: Thiết kế modules để hạn chế dependencies lẫn nhau tối đa
-   **Loose Coupling, High Cohesion**: Mỗi module focus vào single responsibility
-   **Implement First, Declare Later**: Chỉ update mod.rs khi module đã implement và test xong
-   **Post-Implementation Testing**: Mandatory testing sau mỗi implementation
-   **Quality Gates**: Consultation → Implementation → Testing → Integration cycle
-   Separate reusable functions into utils/
-   Comprehensive error handling
-   Extensive logging và monitoring

## Development Process Requirements

### Pre-Implementation Consultation Process

Trước khi implement bất kỳ module hoặc tính năng nào, AI phải thực hiện consultation process:

1. **Purpose và Requirements Clarification**

    - Hỏi rõ về mục đích và yêu cầu cụ thể của tính năng
    - Xác định functional và non-functional requirements
    - Clarify expected behavior và edge cases

2. **Feature Specification và Acceptance Criteria**

    - Xác nhận các features mong muốn chi tiết
    - Define acceptance criteria rõ ràng
    - Identify potential limitations hoặc constraints

3. **Implementation Strategy Discussion**

    - Thảo luận về approach và implementation strategy user muốn sử dụng
    - Consider alternative methods và evaluate pros/cons
    - Get user input về preferred approach

4. **Library Selection Consultation**

    - Đề xuất library choices với detailed rationale
    - Explain benefits/drawbacks của từng option
    - Follow user-driven selection principle

5. **Implementation Plan Proposal**
    - Phác thảo implementation plan đơn giản và rõ ràng
    - Break down into manageable steps
    - Present for user approval trước khi coding

### Post-Implementation Testing Requirements

Sau khi hoàn thành implementation của mỗi module/tính năng:

1. **Functionality Demonstration**

    - Tích hợp module vào `src/main.rs` để demonstrate functionality (nếu phù hợp)
    - Hoặc tạo dedicated test file trong thư mục `tests/` để verify correctness
    - Ensure module chạy độc lập theo independence principles

2. **Testing và Validation**

    - Test phải validate core functionality và edge cases
    - Cover error scenarios và performance expectations
    - Verify module integration capabilities

3. **Configuration Management**

    - Nếu module yêu cầu configuration để hoạt động:
    - Yêu cầu user cung cấp các config values cần thiết
    - Document clearly các config parameters required
    - Provide example configuration format
    - Test với actual config values

4. **Completion Criteria**
    - Module chỉ được consider "complete" khi:
    - Tests pass successfully
    - Functionality verified và working as expected
    - User confirms satisfaction với implementation
    - Ready for integration với other modules

## Technical Challenges

### Key Implementation Challenges

1. **Multi-endpoint Management**: Connection pooling, failover logic
2. **Deduplication Algorithm**: Efficient sliding window implementation
3. **Bincode Deserialization**: High-performance Entry parsing
4. **WebSocket Broadcasting**: Backpressure handling, connection management
5. **Memory Management**: Zero-copy optimizations where possible
6. **Error Recovery**: Graceful handling of network failures

### Performance Optimization Areas

-   Lock-free data structures for high concurrency
-   SIMD optimizations for data processing
-   Memory pool allocation strategies
-   Network buffer optimization
-   CPU affinity và thread pinning considerations
