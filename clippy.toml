cognitive-complexity-threshold = 30
too-many-lines-threshold = 30
too-many-arguments-threshold = 7
type-complexity-threshold = 250

enum-variant-name-threshold = 3

trivial-copy-size-limit = 128

array-size-threshold = 512000
enum-variant-size-threshold = 200

large-error-threshold = 128

unreadable-literal-lint-fractions = true

allow-expect-in-tests = true
allow-unwrap-in-tests = true

verbose-bit-mask-threshold = 1

avoid-breaking-exported-api = true
check-private-items = false

# Code style preferences (enforced manually due to rustfmt limitations)
# - Keep assignment statements on single line
# - Group let declarations together
# - Add empty line between logical groups
