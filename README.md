# Tổng quan

Dự án này là 1 ứng dụng **CLI đơn giản** đ<PERSON><PERSON><PERSON> viết bằng **Rust phiên bản ổn định mới nhất**, nó đóng vai trò như 1 **cầu nối trung gian** (**Proxy**) giữa dịch vụ **[Jito Shredstream](https://docs.jito.wtf/lowlatencytxnfeed)** (hoặc các dịch vụ tương tự) và các client muốn nhận được dữ liệu **đã được xử lý** từ **Jito Shredstream Entries** thành các **giao dịch có thể đọc được**.

# Khái niệm cơ bản

## Jito Shredstream là gì?

Jito Shredstream là dịch vụ của Jito Labs cung cấp **real-time access** đến dữ liệu blockchain Solana với **độ trễ cực thấp**. Thay vì phải chờ blocks được finalized on-chain, Shredstream cho phép nhận dữ liệu ngay khi nó được tạo ra bởi các leaders trong network.

## Entry vs Shred vs Transaction

-   **Shred**: Đơn vị dữ liệu nhỏ nhất trong Solana network, được sử dụng để propagate data qua Turbine protocol
-   **Entry**: Cấu trúc dữ liệu chứa một nhóm transactions cùng với hash và proof-of-history information
-   **Transaction**: Đơn vị thực thi logic trên Solana blockchain

**Mối quan hệ**: Shreds → Entries → Transactions

## Tại sao gọi là "Shreder"?

Mặc dù Jito Shredstream service thực tế trả về **Entry objects** (không phải raw shreds), tên dự án "Shreder" phản ánh việc xử lý dữ liệu từ **Shredstream ecosystem**. Ứng dụng này nhận entries từ Shredstream, extract và process các transactions bên trong.

## Cấu trúc Entry Data

Mỗi Entry từ Jito Shredstream chứa:

-   **slot**: Slot number trong Solana blockchain
-   **entries**: Binary data (serialized bằng bincode) chứa array của Entry objects
-   Mỗi Entry object chứa: hash, num_hashes, và array của transactions

## Tại sao cần real-time processing?

-   **Trading applications**: Cần phản ứng ngay lập tức với market movements
-   **MEV (Maximal Extractable Value)**: Cơ hội arbitrage chỉ tồn tại trong milliseconds
-   **Monitoring systems**: Theo dõi specific accounts hoặc programs real-time
-   **Analytics platforms**: Thu thập và phân tích dữ liệu blockchain với độ trễ tối thiểu

# Tại sao cần Shreder?

## Challenges của việc sử dụng Jito Shredstream trực tiếp

### **1. Complexity của Raw Entry Data**

-   Entry data được serialize bằng bincode, cần deserialize để sử dụng
-   Mỗi Entry chứa multiple transactions, cần extract và parse từng transaction
-   Cấu trúc dữ liệu phức tạp với hash chains và proof-of-history information

### **2. Multiple Endpoint Management**

-   Jito cung cấp multiple endpoints để đảm bảo redundancy
-   Cần logic để handle connection failures và automatic failover
-   Optimize latency bằng cách chọn endpoint gần nhất hoặc fastest response

### **3. Data Deduplication**

-   Cùng một entry có thể được nhận từ multiple endpoints
-   Cần identify và loại bỏ duplicate data để tránh double processing
-   Deduplication logic phải efficient để không impact latency

### **4. Filtering và Processing Overhead**

-   Raw stream chứa tất cả entries, cần filter để chỉ lấy relevant data
-   Client applications thường chỉ quan tâm đến specific accounts hoặc programs
-   Processing overhead có thể impact performance của main application

## Giá trị của Shreder Proxy

### **1. Simplified Interface**

-   Cung cấp clean WebSocket API thay vì complex GRPC
-   Pre-processed transaction data sẵn sàng sử dụng
-   Standardized data format cho multiple client applications

### **2. Performance Optimization**

-   Centralized processing giảm load cho individual clients
-   Optimized deduplication và filtering algorithms
-   Connection pooling và intelligent endpoint selection

### **3. Reliability và Redundancy**

-   Automatic failover giữa multiple Jito endpoints
-   Error handling và recovery mechanisms
-   Continuous monitoring và health checks

### **4. Scalability**

-   Một Shreder instance có thể serve multiple clients
-   Reduced bandwidth usage cho clients (chỉ nhận relevant data)
-   Horizontal scaling capability cho high-throughput scenarios

# Chức năng chính

Ứng dụng này sẽ kết nối đến **nhiều endpoint của JitoShredstream cùng 1 lúc**, lắng nghe các **entries** được gửi đến (**lọc các entries trùng lặp** khi chúng cùng đến từ **các endpoint khác nhau**), **deserialize** và **extract** các giao dịch có chứa trong các entries, **lọc** và **xử lý** giao dịch để **loại bỏ** các giao dịch **không phù hợp**, **thêm các thông tin hữu ích** vào giao dịch, sau đó **stream** chúng cho các client đang lắng nghe thông qua giao thức **WebSocket**.

# Luồng hoạt động

## Overview

```
Multiple Jito Endpoints → Shreder (Entry Processing) → WebSocket Clients
        (Raw Entries)              (Clean Transactions)        (Filtered Data)
```

## Chi tiết từng bước

### **1. Multi-Endpoint Connection Setup**

Kết nối đồng thời đến nhiều Jito Shredstream endpoints thông qua GRPC protocol ([Proto](protos/shredstream.proto)).

**Tại sao multiple endpoints?**

-   **Redundancy**: Đảm bảo service availability khi một endpoint down
-   **Latency optimization**: Chọn endpoint với response time thấp nhất
-   **Data completeness**: Một số entries có thể miss từ individual endpoints

### **2. Subscription với Intelligent Filtering**

Gửi SubscribeEntriesRequest với SubscribeRequestFilterAccounts chứa danh sách accounts cần monitor.

**Filter capabilities:**

-   **Account-based**: Monitor specific account addresses
-   **Owner-based**: Track accounts owned by specific programs
-   **Memcmp filters**: Advanced filtering based on account data content
-   **Transaction filters**: Include/exclude based on transaction characteristics

### **3. Real-time Entry Reception và Deduplication**

Nhận stream của Entry objects từ multiple endpoints, thực hiện deduplication dựa trên slot number và entry hash.

**Deduplication strategy:**

-   Identify entries bằng combination của slot + entry hash
-   Maintain sliding window của recent entries để detect duplicates
-   Prioritize entries từ fastest/most reliable endpoints

### **4. Entry Deserialization và Transaction Extraction**

Deserialize binary entry data bằng bincode để lấy Vec<Entry>, sau đó extract transactions từ mỗi Entry.

**Processing steps:**

-   Bincode deserialization của entries field
-   Iterate qua từng Entry trong Vec
-   Extract transactions array từ mỗi Entry
-   Validate transaction structure và signatures

### **5. Transaction Filtering và Enrichment**

Filter transactions để chỉ giữ lại những transactions có chứa accounts đã subscribe, đồng thời thêm metadata hữu ích.

**Filtering criteria:**

-   Match against subscribed account addresses
-   Apply custom business logic filters

**Enrichment data:**

-   Timestamp information
-   Slot và block context
-   Processing Time (From Received To Broadcast, in nanoseconds)

### **6. Real-time WebSocket Broadcasting**

Stream processed transactions đến connected clients qua WebSocket protocol với format chuẩn hóa.

**Broadcasting features:**

-   Individual client subscriptions
-   Topic-based routing
-   Backpressure handling
-   Connection health monitoring

# Mục tiêu dự án

-   Dữ liệu từ khi nhận được từ Jito Shredstream đến khi broadcast cho user phải nhanh nhất có thể (nanoseconds)
-   Dữ liệu phải chính xác 100%
-   Xử lý được hàng trăm nghìn entries cùng 1 lúc
-   Tận dụng được tối đa hiệu năng của máy tính
-   Tối ưu tốt cho production
-   Chạy liên tục 24/7, zero downtime
-   Code đơn giản, ngắn gọn nhưng tối ưu, đáp ứng tốt nhu cầu

# Yêu cầu

-   Tuân thủ nghiêm ngặt các quy định của dự án
-   Tôn trọng cấu trúc thư mục, file có sẵn, không được tự ý thay đổi khi chưa có sự cho phép
-   Sử dụng Rust phiên bản ổn định mới nhất hiện tại
-   Sử dụng các thư viện được cập nhật thường xuyên nhất, có nhiều lượt tải nhất, và được cộng đồng tin tưởng và đánh giá cao nhất
-   Không cài quá nhiều thư viện, chỉ sử dụng những thư viện thật sự cần thiết cho ứng dụng
-   Tách các phần có thể tái sử dụng vào thư mục `utils` và chia chúng thành nhiều file theo loại của mỗi function đó

# Cấu trúc thư mục

```
shreder/
├── src/                    # Mã nguồn chính của ứng dụng
│   ├── main.rs            # Entry point của ứng dụng
│   ├── common/            # Các module chung
│   │   ├── mod.rs         # Module declarations
│   │   ├── broadcaster.rs # WebSocket broadcasting logic
│   │   ├── client.rs      # GRPC client implementation
│   │   ├── monitor.rs     # System monitoring utilities
│   │   ├── processor.rs   # Entry processing logic
│   │   └── server.rs      # Server management
│   ├── config/            # Cấu hình ứng dụng
│   │   ├── mod.rs         # Module declarations
│   │   ├── endpoints.rs   # GRPC endpoints configuration
│   │   ├── filters.rs     # Transaction filtering rules
│   │   ├── logger.rs      # Logging configuration
│   │   ├── monitor.rs     # Monitoring configuration
│   │   └── server.rs      # Server configuration
│   ├── core/              # Core functionality
│   │   ├── mod.rs         # Module declarations
│   │   ├── config.rs      # Core configuration management
│   │   └── logger.rs      # Core logging implementation
│   └── utils/             # Utility functions có thể tái sử dụng
│       └── mod.rs         # Module declarations
├── protos/                # Protocol Buffer definitions
│   └── shredstream.proto  # GRPC service definitions cho Jito Shredstream
├── releases/              # Binary files sau khi build
│   ├── shreder            # Binary cho platform hiện tại
│   ├── shreder-linux-amd64 # Binary cho Linux AMD64
│   └── shreder-macos-arm64 # Binary cho macOS ARM64
├── target/                # Cargo build artifacts (auto-generated)
├── .vscode/               # VSCode workspace settings
├── .cargo/                # Cargo configuration
├── Cargo.toml             # Package configuration và dependencies
├── Cargo.lock             # Dependency lock file (auto-generated)
├── Makefile               # Build scripts và automation commands
├── clippy.toml            # Clippy linting configuration
└── README.md              # Tài liệu dự án
```

## Mô tả chi tiết các thư mục

### `src/common/`

Chứa các module chung được sử dụng xuyên suốt ứng dụng:

-   **broadcaster.rs**: Logic broadcast dữ liệu qua WebSocket cho clients
-   **client.rs**: Implementation GRPC client để kết nối với Jito Shredstream
-   **monitor.rs**: Utilities để monitor hiệu năng và trạng thái hệ thống
-   **processor.rs**: Logic xử lý và deserialize entries thành transactions
-   **server.rs**: Quản lý server và connection handling

### `src/config/`

Chứa các module cấu hình cho từng thành phần:

-   **endpoints.rs**: Cấu hình các GRPC endpoints của Jito Shredstream
-   **filters.rs**: Định nghĩa rules để filter transactions
-   **logger.rs**: Cấu hình logging system
-   **monitor.rs**: Cấu hình monitoring và metrics
-   **server.rs**: Cấu hình WebSocket server

### `src/core/`

Chứa core functionality không phụ thuộc vào business logic:

-   **config.rs**: Core configuration management
-   **logger.rs**: Core logging implementation

### `src/utils/`

Chứa các utility functions có thể tái sử dụng, được tổ chức theo loại function

# Tài liệu tham khảo

-   [Jito Shredstream](https://docs.jito.wtf/lowlatencytxnfeed)
-   [Shredstream Client Repository](https://github.com/jito-labs/shredstream-proxy)
-   [Example To Decode Entry From Shred](https://raw.githubusercontent.com/jito-labs/shredstream-proxy/refs/heads/master/examples/deshred.rs)
-   [Solana Agave Validator Document](https://docs.anza.xyz/)
-   [Solana Agave Client](https://github.com/anza-xyz/agave)
-   [Solana Entry Struct Document in solana_entry crate](https://docs.rs/solana-entry/latest/solana_entry/entry/struct.Entry.html)
-   [solana_entry crate sourcecode](https://github.com/anza-xyz/agave/tree/master/entry)

# Lưu ý

-   Nếu cần đọc các file nằm ngoài workspace của dự án này, sử dụng MCP server **File** đã được cài sẵn
-   Với các file nằm trong phạm vi workspace, ưu tiên sử dụng các **buitin tool**
-   Sử dụng MCP server **Github** khi cần truy cập vào các repository trên Github
-   Sử dụng **builtin Web Browser** với các liên kết không thể đọc được bằng MCP server **Fetch**
